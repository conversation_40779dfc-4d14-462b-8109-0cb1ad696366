import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/support_model.dart';

class SupportService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = 'support_requests';

  /// Submit a support request
  static Future<bool> submitSupportRequest({
    String? referenceId,
    required String contactNumber,
    required String address,
    required String category,
    required String message,
  }) async {
    try {
      final docRef = _firestore.collection(_collection).doc();
      final now = DateTime.now();

      final supportRequest = SupportModel(
        id: docRef.id,
        referenceId: referenceId,
        contactNumber: contactNumber,
        address: address,
        category: category,
        message: message,
        status: SupportStatus.pending,
        createdAt: now,
        updatedAt: now,
      );

      await docRef.set(supportRequest.toMap());
      return true;
    } catch (e) {
      print('Error submitting support request: $e');
      return false;
    }
  }

  /// Get user's support requests
  static Future<List<SupportModel>> getUserSupportRequests({
    String? referenceId,
    String? contactNumber,
    int limit = 20,
  }) async {
    try {
      List<SupportModel> allRequests = [];
      Set<String> seenIds = {}; // To avoid duplicates

      // Query 1: Get requests by referenceId (user ID)
      if (referenceId != null && referenceId.isNotEmpty) {
        Query query1 = _firestore.collection(_collection)
            .where('referenceId', isEqualTo: referenceId)
            .orderBy('createdAt', descending: true)
            .limit(limit);

        final querySnapshot1 = await query1.get();
        final requests1 = querySnapshot1.docs
            .map((doc) => SupportModel.fromDocument(doc))
            .toList();

        for (final request in requests1) {
          if (!seenIds.contains(request.id)) {
            allRequests.add(request);
            seenIds.add(request.id);
          }
        }
      }

      // Query 2: Get requests by contactNumber
      if (contactNumber != null && contactNumber.isNotEmpty) {
        Query query2 = _firestore.collection(_collection)
            .where('contactNumber', isEqualTo: contactNumber)
            .orderBy('createdAt', descending: true)
            .limit(limit);

        final querySnapshot2 = await query2.get();
        final requests2 = querySnapshot2.docs
            .map((doc) => SupportModel.fromDocument(doc))
            .toList();

        for (final request in requests2) {
          if (!seenIds.contains(request.id)) {
            allRequests.add(request);
            seenIds.add(request.id);
          }
        }
      }

      // Sort all requests by creation date (newest first) and limit results
      allRequests.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      // Apply limit to the combined results
      if (allRequests.length > limit) {
        allRequests = allRequests.take(limit).toList();
      }

      return allRequests;
    } catch (e) {
      print('Error getting user support requests: $e');
      return [];
    }
  }

  /// Get support request by ID
  static Future<SupportModel?> getSupportRequestById(String id) async {
    try {
      final doc = await _firestore.collection(_collection).doc(id).get();
      if (doc.exists) {
        return SupportModel.fromDocument(doc);
      }
      return null;
    } catch (e) {
      print('Error getting support request: $e');
      return null;
    }
  }
}
