import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/user_model.dart';
import '../models/notification_model.dart';
import 'notification_service.dart';
import 'navigation_service.dart';

// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  print('Handling a background message: ${message.messageId}');
  print('Message data: ${message.data}');

  // Store notification in local database if it's from admin
  await FCMService()._storeNotificationFromFCM(message);
}

class FCMService {
  static final FCMService _instance = FCMService._internal();
  factory FCMService() => _instance;
  FCMService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();

  String? _currentToken;
  String? get currentToken => _currentToken;

  // Initialize FCM service
  Future<void> initialize() async {
    try {
      // Set the background messaging handler
      FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

      // Initialize local notifications
      await _initializeLocalNotifications();

      // Request permission for notifications
      await _requestPermission();

      // Get initial token
      await _getAndStoreToken();

      // Listen for token refresh
      _listenForTokenRefresh();

      // Handle foreground messages
      _handleForegroundMessages();

      // Handle notification taps
      _handleNotificationTaps();

      print('FCM Service initialized successfully');
    } catch (e) {
      print('Error initializing FCM Service: $e');
    }
  }

  // Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await _localNotifications.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTap,
    );
  }

  // Request notification permissions
  Future<bool> _requestPermission() async {
    try {
      NotificationSettings settings = await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      print('User granted permission: ${settings.authorizationStatus}');
      return settings.authorizationStatus == AuthorizationStatus.authorized ||
          settings.authorizationStatus == AuthorizationStatus.provisional;
    } catch (e) {
      print('Error requesting permission: $e');
      return false;
    }
  }

  // Get and store FCM token
  Future<void> _getAndStoreToken() async {
    try {
      String? token = await _firebaseMessaging.getToken();
      if (token != null) {
        _currentToken = token;
        print('FCM Token obtained');
        await _storeTokenInFirestore(token);
      } else {
        print('Failed to get FCM token');
      }
    } catch (e) {
      print('Error getting FCM token: $e');
    }
  }

  // Store token in Firestore (only when user is authenticated)
  Future<void> _storeTokenInFirestore(String token) async {
    try {
      // Only store token if user is authenticated
      final currentUser = _auth.currentUser;
      if (currentUser != null) {
        await _firestore.collection('fcm_tokens').doc(token).set({
          'token': token,
          'userId': currentUser.uid,
          'platform': 'mobile',
          'createdAt': FieldValue.serverTimestamp(),
          'lastUsed': FieldValue.serverTimestamp(),
          'isActive': true,
        });
        print('FCM token stored successfully');
      }
    } catch (e) {
      print('Error storing token: $e');
    }
  }

  // Store token for authenticated user
  Future<void> storeTokenForUser(String userId) async {
    if (_currentToken != null) {
      try {
        await _firestore.collection('users').doc(userId).update({
          'fcmTokens': FieldValue.arrayUnion([_currentToken]),
          'lastTokenUpdate': FieldValue.serverTimestamp(),
        });

        // Also store in separate tokens collection for easier querying
        await _firestore.collection('fcm_tokens').doc(_currentToken).set({
          'token': _currentToken,
          'userId': userId,
          'platform': 'mobile',
          'createdAt': FieldValue.serverTimestamp(),
          'lastUsed': FieldValue.serverTimestamp(),
          'isActive': true,
        });

        print('Successfully stored FCM token for user: $userId');
      } catch (e) {
        print('Error storing token for user: $e');
      }
    }
  }





  // Listen for token refresh
  void _listenForTokenRefresh() {
    _firebaseMessaging.onTokenRefresh.listen((String token) {
      _currentToken = token;
      print('FCM Token refreshed');
      _storeTokenInFirestore(token);

      // Also update user document if user is authenticated
      final currentUser = _auth.currentUser;
      if (currentUser != null) {
        storeTokenForUser(currentUser.uid);
      }
    });
  }

  // Handle foreground messages
  void _handleForegroundMessages() {
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      print('Got a message whilst in the foreground!');
      print('Message data: ${message.data}');

      if (message.notification != null) {
        // Store notification in local database if it's from admin
        _storeNotificationFromFCM(message);

        _showLocalNotification(message);
      }
    });
  }

  // Show local notification for foreground messages
  Future<void> _showLocalNotification(RemoteMessage message) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'high_importance_channel',
      'High Importance Notifications',
      channelDescription: 'This channel is used for important notifications.',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: false,
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails();

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    // Create payload with navigation data
    final payload = {
      'screen': message.data['screen'] ?? 'notification',
      'action': message.data['action'] ?? 'open_notifications',
      'source': 'fcm_foreground',
      ...message.data,
    };

    await _localNotifications.show(
      message.hashCode,
      message.notification?.title,
      message.notification?.body,
      platformChannelSpecifics,
      payload: payload.toString(),
    );
  }

  // Handle notification taps
  void _handleNotificationTaps() {
    // Handle notification tap when app is terminated
    FirebaseMessaging.instance.getInitialMessage().then((RemoteMessage? message) {
      if (message != null) {
        print('App opened from terminated state via notification');
        _handleNotificationTap(message);
      }
    });

    // Handle notification tap when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      print('App opened from background via notification');
      _handleNotificationTap(message);
    });
  }

  // Handle notification tap
  void _handleNotificationTap(RemoteMessage message) {
    print('Notification tapped: ${message.data}');
    
    // Navigate based on notification data
    final data = message.data;
    if (data.containsKey('screen')) {
      _navigateToScreen(data['screen'], data);
    }
  }

  // Handle local notification tap
  void _onNotificationTap(NotificationResponse response) {
    print('Local notification tapped: ${response.payload}');

    if (response.payload != null && response.payload!.isNotEmpty) {
      try {
        // Parse the payload data
        final payloadData = response.payload!;

        // If payload contains navigation data, handle it
        if (payloadData.contains('screen')) {
          // Extract screen info and navigate
          _navigateToScreen('notification', {'source': 'local_notification'});
        } else {
          // Default navigation to notifications screen
          _navigateToScreen('notification', {'source': 'local_notification'});
        }
      } catch (e) {
        print('Error parsing notification payload: $e');
        // Default navigation to notifications screen
        _navigateToScreen('notification', {'source': 'local_notification'});
      }
    } else {
      // Default navigation to notifications screen
      _navigateToScreen('notification', {'source': 'local_notification'});
    }
  }

  // Store notification from FCM in local database
  Future<void> _storeNotificationFromFCM(RemoteMessage message) async {
    try {
      // Only store if it's from admin and has notification content
      if (message.notification != null) {
        // Get current user ID from Firebase Auth
        final currentUserId = await _getCurrentUserId();

        if (currentUserId != null) {
          final success = await NotificationService.createNotification(
            userId: currentUserId,
            fromUserId: 'admin',
            fromUserName: 'Admin',
            fromUserAvatar: '',
            type: NotificationType.system,
            title: message.notification!.title ?? '',
            message: message.notification!.body ?? '',
            data: message.data,
          );

          if (success) {
            print('Successfully stored FCM notification in local database for user: $currentUserId');
          } else {
            print('Failed to store FCM notification in local database');
          }
        } else {
          print('Cannot store FCM notification: User not authenticated');
        }
      }
    } catch (e) {
      print('Error storing FCM notification: $e');
    }
  }

  // Get current user ID from Firebase Auth
  Future<String?> _getCurrentUserId() async {
    try {
      final user = _auth.currentUser;
      return user?.uid;
    } catch (e) {
      print('Error getting current user ID: $e');
      return null;
    }
  }

  // Navigate to specific screen based on notification data
  void _navigateToScreen(String screen, Map<String, dynamic> data) {
    final context = NavigationService.navigatorKey.currentContext;
    if (context != null) {
      switch (screen) {
        case 'chat':
          Navigator.pushNamed(context, '/chat');
          break;
        case 'profile':
          if (data.containsKey('userId')) {
            Navigator.pushNamed(context, '/profile', arguments: data['userId']);
          }
          break;
        case 'notification':
          Navigator.pushNamed(context, '/notifications');
          break;
        default:
          Navigator.pushNamed(context, '/main');
      }
    }
  }

  // Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    final settings = await _firebaseMessaging.getNotificationSettings();
    return settings.authorizationStatus == AuthorizationStatus.authorized;
  }

  // Remove token when user logs out
  Future<void> removeTokenForUser(String userId) async {
    if (_currentToken != null) {
      try {
        await _firestore.collection('users').doc(userId).update({
          'fcmTokens': FieldValue.arrayRemove([_currentToken]),
        });
        
        await _firestore.collection('fcm_tokens').doc(_currentToken).update({
          'isActive': false,
          'removedAt': FieldValue.serverTimestamp(),
        });
      } catch (e) {
        print('Error removing token for user: $e');
      }
    }
  }
}


