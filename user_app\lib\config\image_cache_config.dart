import 'package:flutter/foundation.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:cached_network_image/cached_network_image.dart';

class ImageCacheConfig {
  // Cache managers for different image types
  static final CacheManager productImageCacheManager = CacheManager(
    Config(
      'product_images',
      stalePeriod: const Duration(days: 7), // Keep images for 7 days
      maxNrOfCacheObjects: 200, // Maximum 200 cached images
      repo: JsonCacheInfoRepository(databaseName: 'product_images'),
      fileService: HttpFileService(),
    ),
  );

  static final CacheManager profileImageCacheManager = CacheManager(
    Config(
      'profile_images',
      stalePeriod: const Duration(days: 30), // Keep profile images longer
      maxNrOfCacheObjects: 100,
      repo: JsonCacheInfoRepository(databaseName: 'profile_images'),
      fileService: HttpFileService(),
    ),
  );

  static final CacheManager postImageCacheManager = CacheManager(
    Config(
      'post_images',
      stalePeriod: const Duration(days: 3), // Posts change more frequently
      maxNrOfCacheObjects: 150,
      repo: JsonCacheInfoRepository(databaseName: 'post_images'),
      fileService: HttpFileService(),
    ),
  );

  // Optimized settings for different image types and platforms
  static Map<String, dynamic> getOptimalSettings(String imageType) {
    // Base settings for Android optimization
    Map<String, dynamic> baseSettings = {
      'memCacheWidth': null,
      'memCacheHeight': null,
      'maxWidthDiskCache': null,
      'maxHeightDiskCache': null,
    };

    switch (imageType) {
      case 'product_card':
        return {
          ...baseSettings,
          'memCacheWidth': 400, // Optimized for product cards
          'memCacheHeight': 400,
          'maxWidthDiskCache': 800,
          'maxHeightDiskCache': 800,
        };
      
      case 'product_preview':
        return {
          ...baseSettings,
          'memCacheWidth': 200, // Smaller for previews
          'memCacheHeight': 200,
          'maxWidthDiskCache': 400,
          'maxHeightDiskCache': 400,
        };
      
      case 'product_detail':
        return {
          ...baseSettings,
          'memCacheWidth': 800, // Higher quality for detail view
          'memCacheHeight': 800,
          'maxWidthDiskCache': 1200,
          'maxHeightDiskCache': 1200,
        };
      
      case 'profile_image':
        return {
          ...baseSettings,
          'memCacheWidth': 150,
          'memCacheHeight': 150,
          'maxWidthDiskCache': 300,
          'maxHeightDiskCache': 300,
        };
      
      case 'post_image':
        return {
          ...baseSettings,
          'memCacheWidth': 600,
          'memCacheHeight': 600,
          'maxWidthDiskCache': 1080,
          'maxHeightDiskCache': 1080,
        };
      
      default:
        return baseSettings;
    }
  }

  // Clear specific cache
  static Future<void> clearCache(String cacheType) async {
    switch (cacheType) {
      case 'product':
        await productImageCacheManager.emptyCache();
        break;
      case 'profile':
        await profileImageCacheManager.emptyCache();
        break;
      case 'post':
        await postImageCacheManager.emptyCache();
        break;
      case 'all':
        await productImageCacheManager.emptyCache();
        await profileImageCacheManager.emptyCache();
        await postImageCacheManager.emptyCache();
        break;
    }
  }

  // Get cache size
  static Future<int> getCacheSize(String cacheType) async {
    switch (cacheType) {
      case 'product':
        final files = await productImageCacheManager.getFileFromCache('');
        return files?.file.lengthSync() ?? 0;
      case 'profile':
        final files = await profileImageCacheManager.getFileFromCache('');
        return files?.file.lengthSync() ?? 0;
      case 'post':
        final files = await postImageCacheManager.getFileFromCache('');
        return files?.file.lengthSync() ?? 0;
      default:
        return 0;
    }
  }

  // Preload important images
  static Future<void> preloadImage(String imageUrl, String cacheType) async {
    try {
      CacheManager manager;
      switch (cacheType) {
        case 'product':
          manager = productImageCacheManager;
          break;
        case 'profile':
          manager = profileImageCacheManager;
          break;
        case 'post':
          manager = postImageCacheManager;
          break;
        default:
          manager = productImageCacheManager;
      }
      
      await manager.downloadFile(imageUrl);
    } catch (e) {
      if (kDebugMode) {
        print('Failed to preload image: $e');
      }
    }
  }

  // Initialize cache settings
  static void initialize() {
    // Set global cache settings for CachedNetworkImage
    if (kDebugMode) {
      print('ImageCacheConfig: Initializing cache managers');
    }
  }
}
